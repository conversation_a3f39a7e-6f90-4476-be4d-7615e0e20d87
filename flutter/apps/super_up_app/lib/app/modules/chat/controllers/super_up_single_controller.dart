// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:get_it/get_it.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:v_chat_message_page/v_chat_message_page.dart';

import '../../../core/api_service/gifts/gifts_api_service.dart';

/// Custom single controller that extends VSingleController with gifts support
class SuperUpSingleController extends VSingleController {
  SuperUpSingleController({
    required super.vRoom,
    required super.messageProvider,
    required super.scrollController,
    required super.inputStateController,
    required super.itemController,
    required super.singleAppBarController,
    required super.vMessageConfig,
    required super.language,
  });

  @override
  Future<List<Gift>> giftsFetcher() async {
    try {
      final giftsService = GetIt.I.get<GiftsApiService>();
      return await giftsService.getGifts();
    } catch (e) {
      // If service is not available, return empty list
      return [];
    }
  }
}
