// Copyright 2023, the hate<PERSON><PERSON><PERSON> project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/material.dart';
import 'package:scroll_to_index/scroll_to_index.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:v_chat_message_page/v_chat_message_page.dart';
import 'package:v_chat_sdk_core/v_chat_sdk_core.dart';

import '../../../v_chat_v2/translations.dart';
import '../controllers/super_up_single_controller.dart';

/// Example single chat page with gifts support
class SuperUpSingleChatPage extends StatefulWidget {
  final VRoom vRoom;
  final VMessageConfig vMessageConfig;

  const SuperUpSingleChatPage({
    super.key,
    required this.vRoom,
    required this.vMessageConfig,
  });

  @override
  State<SuperUpSingleChatPage> createState() => _SuperUpSingleChatPageState();
}

class _SuperUpSingleChatPageState extends State<SuperUpSingleChatPage> {
  late final SuperUpSingleController controller;

  @override
  void initState() {
    super.initState();
    final provider = MessageProvider();
    controller = SuperUpSingleController(
      vRoom: widget.vRoom,
      language: vMessageLocalizationPageModel(context),
      vMessageConfig: widget.vMessageConfig,
      singleAppBarController: SingleAppBarController(
        vRoom: widget.vRoom,
        messageProvider: provider,
      ),
      messageProvider: provider,
      scrollController: AutoScrollController(
        axis: Axis.vertical,
        suggestedRowHeight: 200,
      ),
      inputStateController: InputStateController(widget.vRoom),
      itemController: VMessageItemController(
        messageProvider: provider,
        context: context,
        vMessageConfig: widget.vMessageConfig,
      ),
    );
  }

  @override
  void dispose() {
    controller.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return VSingleView(
      vRoom: widget.vRoom,
      vMessageConfig: widget.vMessageConfig,
      language: vMessageLocalizationPageModel(context),
    );
  }
}
