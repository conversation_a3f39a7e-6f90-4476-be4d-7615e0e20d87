// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/foundation.dart';
import 'package:super_up_core/super_up_core.dart';
import '../api_service/profile/profile_api_service.dart';

class BalanceService extends ChangeNotifier {
  static final BalanceService _instance = BalanceService._internal();
  factory BalanceService() => _instance;
  BalanceService._internal();

  static BalanceService get instance => _instance;

  double _balance = 0.0;
  late ProfileApiService _profileApiService;

  double get balance => _balance;

  /// Initialize balance from backend
  Future<void> init() async {
    try {
      _profileApiService = ProfileApiService.init();
      await _fetchBalanceFromBackend();
    } catch (e) {
      // If initialization fails, start with 0 balance
      _balance = 0.0;
      notifyListeners();
    }
  }

  /// Fetch balance from backend
  Future<void> _fetchBalanceFromBackend() async {
    try {
      final response = await _profileApiService.getBalance();
      _balance = (response['balance'] as num?)?.toDouble() ?? 0.0;
      notifyListeners();
    } catch (e) {
      // If API fails, keep current balance
      print('Failed to fetch balance: $e');
    }
  }

  /// Add amount to balance (when claiming gifts)
  Future<void> addToBalance(double amount) async {
    try {
      final response = await _profileApiService.addToBalance(amount);
      _balance = (response['balance'] as num?)?.toDouble() ?? _balance;
      notifyListeners();
    } catch (e) {
      // If API fails, update locally for now
      _balance += amount;
      notifyListeners();
      rethrow;
    }
  }

  /// Subtract amount from balance (when spending)
  Future<void> subtractFromBalance(double amount) async {
    if (_balance >= amount) {
      try {
        final response = await _profileApiService.subtractFromBalance(amount);
        _balance = (response['balance'] as num?)?.toDouble() ?? _balance;
        notifyListeners();
      } catch (e) {
        // If API fails, update locally for now
        _balance -= amount;
        notifyListeners();
        rethrow;
      }
    }
  }

  /// Set balance to a specific amount (for admin purposes)
  Future<void> setBalance(double amount) async {
    _balance = amount;
    notifyListeners();
  }

  /// Clear balance (on logout)
  Future<void> clearBalance() async {
    _balance = 0.0;
    notifyListeners();
  }

  /// Format balance for display
  String get formattedBalance => '\$${_balance.toStringAsFixed(2)}';
}
