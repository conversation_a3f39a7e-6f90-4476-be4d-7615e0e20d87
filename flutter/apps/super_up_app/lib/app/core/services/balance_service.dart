// Copyright 2023, the hate<PERSON>ragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/foundation.dart';
import 'package:super_up_core/super_up_core.dart';

class BalanceService extends ChangeNotifier {
  static final BalanceService _instance = BalanceService._internal();
  factory BalanceService() => _instance;
  BalanceService._internal();

  static BalanceService get instance => _instance;

  double _balance = 0.0;

  double get balance => _balance;

  /// Initialize balance from storage
  Future<void> init() async {
    final savedBalanceString = VAppPref.getStringOrNullKey('user_balance');
    _balance = savedBalanceString != null
        ? double.tryParse(savedBalanceString) ?? 0.0
        : 0.0;
    notifyListeners();
  }

  /// Add amount to balance (when claiming gifts)
  Future<void> addToBalance(double amount) async {
    _balance += amount;
    await _saveBalance();
    notifyListeners();
  }

  /// Subtract amount from balance (when spending)
  Future<void> subtractFromBalance(double amount) async {
    if (_balance >= amount) {
      _balance -= amount;
      await _saveBalance();
      notifyListeners();
    }
  }

  /// Set balance to a specific amount
  Future<void> setBalance(double amount) async {
    _balance = amount;
    await _saveBalance();
    notifyListeners();
  }

  /// Save balance to storage
  Future<void> _saveBalance() async {
    await VAppPref.setStringKey('user_balance', _balance.toString());
  }

  /// Clear balance (on logout)
  Future<void> clearBalance() async {
    _balance = 0.0;
    await VAppPref.removeKey('user_balance');
    notifyListeners();
  }

  /// Format balance for display
  String get formattedBalance => '\$${_balance.toStringAsFixed(2)}';
}
