// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:super_up_admin/src/core/api_service/s_admin/s_admin_api_service.dart';
import 'package:super_up_admin/src/core/models/gift/admin_gift.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:v_platform/v_platform.dart';

class GiftsController extends SLoadingController<List<AdminGift>> {
  GiftsController() : super(SLoadingState([]));

  final _adminApiService = GetIt.I.get<SAdminApiService>();

  @override
  void onInit() {
    loadGifts();
  }

  @override
  void onClose() {
    // Clean up resources if needed
  }

  Future<void> loadGifts() async {
    setStateLoading();

    await vSafeApiCall<AdminGiftsResponse>(
      request: () async {
        return await _adminApiService.getGifts();
      },
      onSuccess: (response) {
        value.data = response.gifts;
        setStateSuccess();
      },
      onError: (exception, trace) {
        setStateError(exception.toString());
      },
    );
  }

  Future<void> createGift({
    required String name,
    String? description,
    required double price,
    bool? isActive,
    VPlatformFile? imageFile,
  }) async {
    await vSafeApiCall<AdminGift>(
      request: () async {
        return await _adminApiService.createGift(
          name: name,
          description: description,
          price: price,
          isActive: isActive,
          imageFile: imageFile,
        );
      },
      onSuccess: (response) {
        // Add the new gift to the list
        final updatedList = List<AdminGift>.from(value.data);
        updatedList.insert(0, response);
        value.data = updatedList;
        setStateSuccess();
      },
      onError: (exception, trace) {
        // Handle error - could show snackbar
        print('Error creating gift: $exception');
      },
    );
  }

  Future<void> updateGift({
    required String giftId,
    String? name,
    String? description,
    double? price,
    bool? isActive,
    VPlatformFile? imageFile,
  }) async {
    await vSafeApiCall<AdminGift>(
      request: () async {
        return await _adminApiService.updateGift(
          giftId: giftId,
          name: name,
          description: description,
          price: price,
          isActive: isActive,
          imageFile: imageFile,
        );
      },
      onSuccess: (response) {
        // Update the gift in the list
        final updatedList = List<AdminGift>.from(value.data);
        final index = updatedList.indexWhere((gift) => gift.id == giftId);
        if (index != -1) {
          updatedList[index] = response;
          value.data = updatedList;
          setStateSuccess();
        }
      },
      onError: (exception, trace) {
        print('Error updating gift: $exception');
      },
    );
  }

  Future<void> deleteGift(String giftId) async {
    await vSafeApiCall<bool>(
      request: () async {
        return await _adminApiService.deleteGift(giftId);
      },
      onSuccess: (response) {
        // Remove the gift from the list
        final updatedList = List<AdminGift>.from(value.data);
        updatedList.removeWhere((gift) => gift.id == giftId);
        value.data = updatedList;
        setStateSuccess();
      },
      onError: (exception, trace) {
        print('Error deleting gift: $exception');
      },
    );
  }

  void showCreateGiftDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => CreateGiftDialog(
        onCreateGift: createGift,
      ),
    );
  }

  void showEditGiftDialog(BuildContext context, AdminGift gift) {
    showDialog(
      context: context,
      builder: (context) => CreateGiftDialog(
        gift: gift,
        onCreateGift: ({
          required String name,
          String? description,
          required double price,
          bool? isActive,
          VPlatformFile? imageFile,
        }) async {
          await updateGift(
            giftId: gift.id,
            name: name,
            description: description,
            price: price,
            isActive: isActive,
            imageFile: imageFile,
          );
        },
      ),
    );
  }

  void showDeleteConfirmation(BuildContext context, AdminGift gift) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Gift'),
        content: Text('Are you sure you want to delete "${gift.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              deleteGift(gift.id);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}

// Create Gift Dialog Widget
class CreateGiftDialog extends StatefulWidget {
  final AdminGift? gift;
  final Future<void> Function({
    required String name,
    String? description,
    required double price,
    bool? isActive,
    VPlatformFile? imageFile,
  }) onCreateGift;

  const CreateGiftDialog({
    super.key,
    this.gift,
    required this.onCreateGift,
  });

  @override
  State<CreateGiftDialog> createState() => _CreateGiftDialogState();
}

class _CreateGiftDialogState extends State<CreateGiftDialog> {
  final _formKey = GlobalKey<FormState>();
  final _priceController = TextEditingController();
  VPlatformFile? _selectedImage;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.gift != null) {
      _priceController.text = widget.gift!.price.toString();
    }
  }

  @override
  void dispose() {
    _priceController.dispose();
    super.dispose();
  }

  String _getFullImageUrl(String imageKey) {
    if (imageKey.startsWith('http')) {
      return imageKey; // Already a full URL
    }
    // Construct full URL: baseMediaUrl + imageKey
    final baseUrl = SConstants.baseMediaUrl;
    return '$baseUrl$imageKey';
  }

  Future<void> _pickImage() async {
    try {
      final result = await VAppPick.getImage();

      if (result != null && result.bytes != null && result.bytes!.isNotEmpty) {
        setState(() {
          _selectedImage = result;
        });
      }
    } catch (e) {
      // Handle any errors during image picking silently
      // In production, you might want to show a snackbar or toast
    }
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;

    // Check if image is required for new gifts
    if (widget.gift == null && _selectedImage == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select an image for the gift'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      await widget.onCreateGift(
        name:
            'Gift \$${_priceController.text}', // Auto-generate name based on price
        description: null,
        price: double.parse(_priceController.text),
        isActive: true, // Always active by default
        imageFile: _selectedImage,
      );

      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      // Error handling is done in the controller
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    return Dialog(
      insetPadding: EdgeInsets.all(isSmallScreen ? 16 : 40),
      child: Container(
        width: isSmallScreen ? double.infinity : 400,
        constraints: BoxConstraints(
          maxHeight: screenSize.height * 0.8,
          maxWidth: isSmallScreen ? double.infinity : 400,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Theme.of(context).scaffoldBackgroundColor,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(12)),
              ),
              child: Row(
                children: [
                  Icon(
                    widget.gift == null ? Icons.card_giftcard : Icons.edit,
                    color: Theme.of(context).primaryColor,
                    size: isSmallScreen ? 20 : 24,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      widget.gift == null ? 'Create Gift' : 'Edit Gift',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            fontSize: isSmallScreen ? 18 : 20,
                          ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                    iconSize: isSmallScreen ? 20 : 24,
                  ),
                ],
              ),
            ),

            // Content
            Flexible(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
                child: Form(
                  key: _formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Image picker
                      GestureDetector(
                        onTap: _pickImage,
                        child: Container(
                          height: isSmallScreen ? 100 : 120,
                          width: double.infinity,
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: Theme.of(context).dividerColor,
                              width: 2,
                            ),
                            borderRadius: BorderRadius.circular(12),
                            color: Theme.of(context).cardColor,
                          ),
                          child: _selectedImage != null
                              ? ClipRRect(
                                  borderRadius: BorderRadius.circular(10),
                                  child: _selectedImage!.isFromBytes
                                      ? Image.memory(
                                          _selectedImage!.uint8List,
                                          fit: BoxFit.cover,
                                        )
                                      : _selectedImage!.isFromPath
                                          ? Image.file(
                                              File(_selectedImage!
                                                  .fileLocalPath!),
                                              fit: BoxFit.cover,
                                            )
                                          : Container(
                                              decoration: BoxDecoration(
                                                color: Colors.grey[200],
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                              ),
                                              child: const Column(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  Icon(Icons.error,
                                                      size: 40,
                                                      color: Colors.grey),
                                                  Text('Invalid image',
                                                      style: TextStyle(
                                                          color: Colors.grey)),
                                                ],
                                              ),
                                            ),
                                )
                              : widget.gift?.imageUrl != null
                                  ? ClipRRect(
                                      borderRadius: BorderRadius.circular(10),
                                      child: Image.network(
                                        _getFullImageUrl(widget.gift!.imageUrl),
                                        fit: BoxFit.cover,
                                        errorBuilder:
                                            (context, error, stackTrace) {
                                          return Container(
                                            decoration: BoxDecoration(
                                              color: Colors.grey[200],
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                            ),
                                            child: const Column(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                Icon(Icons.error,
                                                    size: 40,
                                                    color: Colors.grey),
                                                Text('Failed to load image',
                                                    style: TextStyle(
                                                        color: Colors.grey)),
                                              ],
                                            ),
                                          );
                                        },
                                      ),
                                    )
                                  : Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          Icons.add_photo_alternate,
                                          size: isSmallScreen ? 32 : 40,
                                          color: Theme.of(context).primaryColor,
                                        ),
                                        const SizedBox(height: 8),
                                        Text(
                                          'Tap to select image *',
                                          style: TextStyle(
                                            color:
                                                Theme.of(context).primaryColor,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          '(Required for new gifts)',
                                          style: TextStyle(
                                            color: Colors.red[600],
                                            fontSize: 12,
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                      ],
                                    ),
                        ),
                      ),
                      SizedBox(height: isSmallScreen ? 12 : 16),

                      // Price field
                      TextFormField(
                        controller: _priceController,
                        decoration: InputDecoration(
                          labelText: 'Price *',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          filled: true,
                          fillColor: Theme.of(context).cardColor,
                          prefixText: '\$ ',
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter a price';
                          }
                          final price = double.tryParse(value);
                          if (price == null || price < 0) {
                            return 'Please enter a valid price';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Actions
            Container(
              padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
              decoration: BoxDecoration(
                color: Theme.of(context).cardColor,
                borderRadius:
                    const BorderRadius.vertical(bottom: Radius.circular(12)),
                border: Border(
                  top: BorderSide(
                    color: Theme.of(context).dividerColor,
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed:
                        _isLoading ? null : () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton(
                    onPressed: _isLoading ? null : _submitForm,
                    child: _isLoading
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : Text(widget.gift == null ? 'Create' : 'Update'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
