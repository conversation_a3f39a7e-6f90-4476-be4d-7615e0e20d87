// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:emoji_picker_flutter/emoji_picker_flutter.dart';
import 'package:flutter/material.dart';
import 'package:v_platform/v_platform.dart';
import '../../models/sticker_models.dart';
import 'api_sticker_picker.dart';

/// Callback for when a sticker is selected
typedef OnStickerSelected = void Function(VSticker sticker);

/// Combined emoji and sticker keyboard widget
class EmojiStickerKeyboard extends StatefulWidget {
  final bool isShowing;
  final TextEditingController controller;
  final OnStickerSelected? onStickerSelected;

  const EmojiStickerKeyboard({
    super.key,
    required this.isShowing,
    required this.controller,
    this.onStickerSelected,
  });

  @override
  State<EmojiStickerKeyboard> createState() => _EmojiStickerKeyboardState();
}

class _EmojiStickerKeyboardState extends State<EmojiStickerKeyboard>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Offstage(
      offstage: !widget.isShowing,
      child: Container(
        height: VPlatforms.isWeb ? MediaQuery.of(context).size.height / 3 : 250,
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          border: Border(
            top: BorderSide(
              color: Colors.grey.shade300,
              width: 0.5,
            ),
          ),
        ),
        child: Column(
          children: [
            // Tab bar
            Container(
              height: 40,
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                border: Border(
                  bottom: BorderSide(
                    color: Colors.grey.shade300,
                    width: 0.5,
                  ),
                ),
              ),
              child: TabBar(
                controller: _tabController,
                indicatorColor: Theme.of(context).primaryColor,
                labelColor: Theme.of(context).primaryColor,
                unselectedLabelColor: Colors.grey.shade600,
                tabs: const [
                  Tab(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.emoji_emotions, size: 20),
                        SizedBox(width: 4),
                        Text('Emoji'),
                      ],
                    ),
                  ),
                  Tab(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.sticky_note_2, size: 20),
                        SizedBox(width: 4),
                        Text('Stickers'),
                      ],
                    ),
                  ),
                  Tab(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.card_giftcard, size: 20),
                        SizedBox(width: 4),
                        Text('Gifts'),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            // Content
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  // Emoji picker
                  EmojiPicker(
                    textEditingController: widget.controller,
                    config: Config(
                      height: VPlatforms.isWeb
                          ? MediaQuery.of(context).size.height / 3 - 40
                          : 210,
                      checkPlatformCompatibility: true,
                    ),
                  ),
                  // API Sticker picker with 3rd party integration
                  ApiStickerPicker(
                    height: VPlatforms.isWeb
                        ? MediaQuery.of(context).size.height / 3 - 40
                        : 210,
                    onStickerSelected: (sticker) {
                      if (widget.onStickerSelected != null) {
                        widget.onStickerSelected!(sticker);
                      }
                    },
                  ),
                  // Gifts picker placeholder
                  SizedBox(
                    height: VPlatforms.isWeb
                        ? MediaQuery.of(context).size.height / 3 - 40
                        : 210,
                    child: const Center(
                      child: Text(
                        'Gifts coming soon!',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
