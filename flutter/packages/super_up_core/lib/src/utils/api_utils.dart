// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:chopper/chopper.dart';

void throwIfNotSuccess(Response res) {
  if (res.isSuccessful) return;
  if (res.statusCode == 400) {
    throw Exception((res.error! as Map<String, dynamic>)['data'].toString());
  } else if (res.statusCode == 404) {
    throw Exception((res.error! as Map<String, dynamic>)['data'].toString());
  } else if (res.statusCode == 403) {
    throw Exception((res.error! as Map<String, dynamic>)['data'].toString());
  }
  if (!res.isSuccessful) {
    throw Exception((res.error! as Map<String, dynamic>)['data'].toString());
  }
}

dynamic extractDataFromResponse(Response res) {
  return (res.body as Map<String, dynamic>)['data'];
}
