// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:super_up_core/super_up_core.dart';

class GiftsApiService {
  static dynamic _giftsApi;

  GiftsApiService._();

  Future<List<Gift>> getGifts() async {
    final res = await _giftsApi!.getGifts();
    throwIfNotSuccess(res);
    final data = extractDataFromResponse(res) as List;
    return data.map((e) => Gift.fromMap(e)).toList();
  }

  static GiftsApiService init({
    Uri? baseUrl,
    String? accessToken,
    Map<String, String>? headers,
    dynamic giftsApi,
  }) {
    _giftsApi ??= giftsApi;
    return GiftsApiService._();
  }
}
